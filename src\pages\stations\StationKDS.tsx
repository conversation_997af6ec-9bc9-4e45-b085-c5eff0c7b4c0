import React, { useState, useEffect, useCallback } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowLeft,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Timer,
  Flame,
  ChefHat,
  Zap,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface OrderItem {
  id: string;
  tableNumber: string;
  orderNumber: string;
  items: {
    id: string;
    name: string;
    quantity: number;
    notes?: string;
    extras?: string;
    status: "pending" | "cooking" | "ready" | "cancelled";
    cookingTime?: number; // in minutes
    startedAt?: Date;
    estimatedCompletion?: Date;
  }[];
  timeReceived: Date;
  priority: "normal" | "rush" | "vip";
  totalItems: number;
  completedItems: number;
  estimatedTotalTime: number; // in minutes
  serverName?: string;
  specialInstructions?: string;
}

const StationKDS: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  // Mock data - replace with actual API calls
  const [orders, setOrders] = useState<OrderItem[]>([
    {
      id: "1",
      tableNumber: "T12",
      orderNumber: "ORD-001",
      items: [
        {
          id: "1-1",
          name: "Grilled Salmon",
          quantity: 2,
          status: "cooking",
          cookingTime: 15,
          startedAt: new Date(Date.now() - 8 * 60 * 1000), // Started 8 minutes ago
          estimatedCompletion: new Date(Date.now() + 7 * 60 * 1000), // 7 minutes remaining
        },
        {
          id: "1-2",
          name: "Caesar Salad",
          quantity: 1,
          notes: "No croutons",
          status: "ready",
          cookingTime: 5,
        },
      ],
      timeReceived: new Date(Date.now() - 12 * 60 * 1000), // 12 minutes ago
      priority: "normal",
      totalItems: 2,
      completedItems: 1,
      estimatedTotalTime: 15,
      serverName: "Sarah",
    },
    {
      id: "2",
      tableNumber: "T15",
      orderNumber: "ORD-002",
      items: [
        {
          id: "2-1",
          name: "Ribeye Steak",
          quantity: 1,
          notes: "Medium rare",
          status: "cooking",
          cookingTime: 20,
          startedAt: new Date(Date.now() - 5 * 60 * 1000), // Started 5 minutes ago
          estimatedCompletion: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes remaining
        },
        {
          id: "2-2",
          name: "Mashed Potatoes",
          quantity: 2,
          status: "ready",
          cookingTime: 10,
        },
        {
          id: "2-3",
          name: "Fries",
          quantity: 1,
          status: "ready",
          notes: "Extra salt",
          extras: "Vinegar, Ketchup",
          cookingTime: 8,
        },
      ],
      timeReceived: new Date(Date.now() - 18 * 60 * 1000), // 18 minutes ago
      priority: "rush",
      totalItems: 3,
      completedItems: 2,
      estimatedTotalTime: 20,
      serverName: "Mike",
      specialInstructions: "Customer has nut allergy",
    },
    {
      id: "3",
      tableNumber: "T08",
      orderNumber: "ORD-003",
      items: [
        {
          id: "3-1",
          name: "Lobster Thermidor",
          quantity: 1,
          status: "pending",
          cookingTime: 25,
        },
        {
          id: "3-2",
          name: "Truffle Risotto",
          quantity: 1,
          status: "pending",
          cookingTime: 18,
        },
      ],
      timeReceived: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
      priority: "vip",
      totalItems: 2,
      completedItems: 0,
      estimatedTotalTime: 25,
      serverName: "Emma",
      specialInstructions: "VIP guest - priority service",
    },
  ]);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Density toggle (persisted)
  const [dense, setDense] = useState<boolean>(() => {
    const stored = localStorage.getItem("kds_dense");
    return stored ? stored === "1" : false;
  });

  // Rush alert sound
  useEffect(() => {
    const hasRush = orders.some(o => o.priority === "rush" && o.completedItems < o.totalItems);
    if (hasRush) {
      const audio = new Audio("/sounds/rush-alert.mp3");
      audio.volume = 0.4;
      audio.play().catch(() => {});
    }
  }, [orders]);
  useEffect(() => {
    localStorage.setItem("kds_dense", dense ? "1" : "0");
  }, [dense]);

  // Search & Filters (persisted)
  const [search, setSearch] = useState<string>(() => localStorage.getItem("kds_search") || "");
  const [priorityFilter, setPriorityFilter] = useState<"all" | "normal" | "rush" | "vip">(
    () => (localStorage.getItem("kds_priority") as any) || "all"
  );
  const [statusFilter, setStatusFilter] = useState<"all" | "pending" | "cooking" | "ready" | "cancelled">(
    () => (localStorage.getItem("kds_status") as any) || "all"
  );
  const [timeFilter, setTimeFilter] = useState<"all" | "0-5" | "5-15" | "15+">(
    () => (localStorage.getItem("kds_time") as any) || "all"
  );
  const [serverFilter, setServerFilter] = useState<string>(
    () => localStorage.getItem("kds_server") || "all"
  );
  useEffect(() => {
    localStorage.setItem("kds_search", search);
  }, [search]);
  useEffect(() => {
    localStorage.setItem("kds_priority", priorityFilter);
  }, [priorityFilter]);
  useEffect(() => {
    localStorage.setItem("kds_status", statusFilter);
  }, [statusFilter]);
  useEffect(() => {
    localStorage.setItem("kds_time", timeFilter);
  }, [timeFilter]);
  useEffect(() => {
    localStorage.setItem("kds_server", serverFilter);
  }, [serverFilter]);

  // Utility functions
  const formatTimeAgo = (date: Date): string => {
    const minutes = Math.floor((currentTime.getTime() - date.getTime()) / (1000 * 60));
    if (minutes < 1) return "Just now";
    if (minutes === 1) return "1 min ago";
    return `${minutes} mins ago`;
  };

  const formatTimeRemaining = (date: Date): string => {
    const minutes = Math.floor((date.getTime() - currentTime.getTime()) / (1000 * 60));

    if (minutes <= 0) return "Ready!";
    if (minutes === 1) return "1 min";
    return `${minutes} mins`;
  };

  const getOrderProgress = (order: OrderItem): number => {
    return Math.round((order.completedItems / order.totalItems) * 100);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "vip": return "from-accent to-primary";
      case "rush": return "from-primary to-secondary";
      default: return "from-secondary to-primary";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending": return <Timer className="h-5 w-5 text-secondary" />;
      case "cooking": return <Flame className="h-5 w-5 text-primary" />;
      case "ready": return <CheckCircle className="h-5 w-5 text-primary" />;
      case "cancelled": return <XCircle className="h-5 w-5 text-destructive" />;
      default: return <AlertCircle className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const handleItemStatusChange = useCallback((orderId: string, itemId: string, newStatus: string) => {
    setOrders(prevOrders =>
      prevOrders.map(order => {
        if (order.id === orderId) {
          const updatedItems = order.items.map(item => {
            if (item.id === itemId) {
              const updatedItem = { ...item, status: newStatus as any };
              if (newStatus === "cooking" && !item.startedAt) {
                updatedItem.startedAt = new Date();
                updatedItem.estimatedCompletion = new Date(Date.now() + (item.cookingTime || 10) * 60 * 1000);
              }
              return updatedItem;
            }
            return item;
          });

          const completedItems = updatedItems.filter(item => item.status === "ready").length;
          return { ...order, items: updatedItems, completedItems };
        }
        return order;
      })
    );
  }, []);

  return (
    <div className="min-h-screen bg-background p-4 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-secondary/10 to-primary/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div className="flex items-center gap-4">
            <Link to="/stations/home">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:scale-105 transition-all duration-200">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Menu
                </Button>
              </motion.div>
            </Link>
            <div className="flex-1">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Kitchen Display System


              </h1>
              <p className="text-muted-foreground mt-1">
                Real-time order management • {currentTime.toLocaleTimeString()}
              </p>
            </div>

              {/* Density toggle */}
              <div className="hidden md:flex items-center gap-2 pr-2">
                <span className="text-xs text-muted-foreground">Compact</span>
                <Switch checked={dense} onCheckedChange={setDense} />
              </div>

          </div>

          {/* Stats Dashboard */}
          <div className="flex gap-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-card/80 backdrop-blur-sm border border-border rounded-xl p-4 text-center shadow-lg"
            >
              <div className="text-2xl font-bold text-primary">{orders.filter(o => o.completedItems === o.totalItems).length}</div>
              <div className="text-xs text-muted-foreground">Completed</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 }}
              className="bg-card/80 backdrop-blur-sm border border-border rounded-xl p-4 text-center shadow-lg"
            >
              <div className="text-2xl font-bold text-secondary">{orders.filter(o => o.completedItems < o.totalItems).length}</div>
              <div className="text-xs text-muted-foreground">In Progress</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              className="bg-card/80 backdrop-blur-sm border border-border rounded-xl p-4 text-center shadow-lg"
            >
              <div className="text-2xl font-bold text-accent-foreground">{orders.filter(o => o.priority === "vip").length}</div>
              <div className="text-xs text-muted-foreground">VIP Orders</div>
            </motion.div>
          </div>
        </motion.div>

        {/* Toolbar: Advanced Filters + Density + Counts */}
        <div className="flex items-center justify-between gap-3 flex-wrap">
          <div className="flex items-center gap-3 flex-wrap">
            <div className="w-56">
              <Input value={search} onChange={(e) => setSearch(e.target.value)} placeholder="Search table/order/items..." />
            </div>
            <div className="w-40">
              <Select value={priorityFilter} onValueChange={(v) => setPriorityFilter(v as any)}>
                <SelectTrigger><SelectValue placeholder="Priority" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="rush">Rush</SelectItem>
                  <SelectItem value="vip">VIP</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-40">
              <Select value={statusFilter} onValueChange={(v) => setStatusFilter(v as any)}>
                <SelectTrigger><SelectValue placeholder="Status" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="cooking">Cooking</SelectItem>
                  <SelectItem value="ready">Ready</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-40">
              <Select value={timeFilter} onValueChange={(v) => setTimeFilter(v as any)}>
                <SelectTrigger><SelectValue placeholder="Time" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Times</SelectItem>
                  <SelectItem value="0-5">0–5 mins</SelectItem>
                  <SelectItem value="5-15">5–15 mins</SelectItem>
                  <SelectItem value="15+">15+ mins</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-40">
              <Select value={serverFilter} onValueChange={(v) => setServerFilter(v)}>
                <SelectTrigger><SelectValue placeholder="Server" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Servers</SelectItem>
                  {[...new Set(orders.map(o => o.serverName).filter(Boolean) as string[])].map(s => (
                    <SelectItem key={s} value={s}>{s}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">Compact</span>
              <Switch checked={dense} onCheckedChange={setDense} />
            </div>
            {/* Priority counts */}
            <div className="flex gap-2 text-xs text-muted-foreground">
              <span className="px-2 py-1 rounded bg-secondary/20">VIP: {orders.filter(o => o.priority === 'vip').length}</span>
              <span className="px-2 py-1 rounded bg-primary/20">Rush: {orders.filter(o => o.priority === 'rush').length}</span>
              <span className="px-2 py-1 rounded bg-muted">Normal: {orders.filter(o => o.priority === 'normal').length}</span>
            </div>
          </div>
        </div>


        {/* Orders Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6"
        >
          <AnimatePresence>
            {orders
              .filter(order => {
                const matchesPriority = priorityFilter === "all" || order.priority === priorityFilter;
                if (!matchesPriority) return false;
                const hay = `${order.tableNumber} ${order.orderNumber} ${order.items.map(i => i.name).join(" ")}`.toLowerCase();
                return hay.includes(search.toLowerCase());
              })
              .filter(order => {
                  // Status filter: include if any item matches
                  if (statusFilter !== "all") {
                    const itemMatch = order.items.some(i => i.status === statusFilter);
                    if (!itemMatch) return false;
                  }
                  // Time filter: mins since received
                  const mins = Math.floor((currentTime.getTime() - order.timeReceived.getTime()) / (1000 * 60));
                  if (timeFilter === "0-5" && !(mins >= 0 && mins <= 5)) return false;
                  if (timeFilter === "5-15" && !(mins >= 5 && mins <= 15)) return false;
                  if (timeFilter === "15+" && !(mins > 15)) return false;
                  // Server filter
                  if (serverFilter !== "all" && order.serverName !== serverFilter) return false;
                  return true;
                })
              .map((order, index) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -20, scale: 0.9 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="group"
              >
                <Card className={`
                  relative overflow-hidden shadow-2xl backdrop-blur-sm
                  ${order.priority === "vip" ? "bg-card/90 border-accent" :
                    order.priority === "rush" ? "bg-card/90 border-primary" :
                    "bg-card/90 border-border"}
                  border transition-all duration-300 group-hover:shadow-3xl
                  flex flex-col ${dense ? 'h-[420px]' : 'h-[520px]'} md:${dense ? 'h-[440px]' : 'h-[560px]'} xl:${dense ? 'h-[460px]' : 'h-[600px]'}
                `}>
                  {/* Priority indicator */}
                  <div className={`
                    absolute top-0 left-0 right-0 h-1 bg-gradient-to-r
                    ${getPriorityColor(order.priority)}
                    ${order.priority === "rush" ? "animate-pulse kds-urgent-order" : ""}
                  `} />

                  {/* VIP Crown */}
                  {order.priority === "vip" && (
                    <motion.div
                      animate={{ rotate: [0, 5, -5, 0] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="absolute top-2 right-2 text-yellow-400"
                    >
                      <ChefHat className="h-6 w-6" />
                    </motion.div>
                  )}

                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-xl font-bold text-foreground flex items-center gap-2">
                        <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                          Table {order.tableNumber}
                        </span>
                        {order.priority === "rush" && (
                          <motion.div animate={{ scale: [1, 1.2, 1] }} transition={{ duration: 1, repeat: Infinity }}>
                            <Zap className="h-5 w-5 text-primary" />
                          </motion.div>
                        )}
                      </CardTitle>
                      <Badge
                        className={`
                          text-xs font-semibold px-3 py-1 rounded-full border-0
                          ${order.priority === "vip" ? "bg-accent text-accent-foreground" :
                            order.priority === "rush" ? "bg-primary text-primary-foreground animate-pulse" :
                            "bg-secondary text-secondary-foreground"}
                        `}
                      >
                        {order.priority.toUpperCase()}
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {formatTimeAgo(order.timeReceived)}
                      </span>
                      <span className="text-muted-foreground">#{order.orderNumber}</span>
                    </div>

                    {/* Progress Bar */}
                    <div className="mt-3">
                      <div className="flex justify-between text-xs text-muted-foreground mb-1">
                        <span>Progress</span>
                        <span>{getOrderProgress(order)}%</span>
                      </div>
                      <Progress
                        value={getOrderProgress(order)}
                        className="h-2 bg-muted"
                      />
                    </div>

                    {/* Server and Special Instructions */}
                    {(order.serverName || order.specialInstructions) && (
                      <div className="mt-2 space-y-1">
                        {order.serverName && (
                          <div className="text-xs text-slate-400">
                            Server: <span className="text-blue-300">{order.serverName}</span>
                          </div>
                        )}
                        {order.specialInstructions && (
                          <div className="text-xs text-yellow-300 bg-yellow-900/20 rounded px-2 py-1">
                            ⚠️ {order.specialInstructions}
                          </div>
                        )}
                      </div>
                    )}
                  </CardHeader>

                  <CardContent className="space-y-4 flex-1 min-h-0 flex flex-col">
                    <ScrollArea className="flex-1 min-h-0">
                      <div className="space-y-3">
                        {order.items.map((item, itemIndex) => (
                          <motion.div
                            key={item.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: itemIndex * 0.1 }}
                            className={`
                              relative border rounded-lg p-3 transition-all duration-300
                              ${item.status === "ready" ? "bg-success/10 border-success/40" :
                                item.status === "cooking" ? "bg-primary/10 border-primary/40" :
                                item.status === "pending" ? "bg-secondary/10 border-secondary/40" :
                                "bg-destructive/10 border-destructive/40"}
                            `}
                          >
                            {/* Cooking timer for active items */}
                            {item.status === "cooking" && item.estimatedCompletion && (
                              <div className="absolute top-1 right-1 text-xs text-orange-300 bg-orange-900/50 rounded px-2 py-1">
                                {formatTimeRemaining(item.estimatedCompletion)}
                              </div>
                            )}

                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <motion.div
                                  animate={item.status === "cooking" ? { rotate: 360 } : {}}
                                  transition={{ duration: 2, repeat: item.status === "cooking" ? Infinity : 0, ease: "linear" }}
                                >
                                  {getStatusIcon(item.status)}
                                </motion.div>
                                <div>
                                  <span className="font-medium text-foreground">{item.name}</span>
                                  {item.cookingTime && (
                                    <div className="text-xs text-muted-foreground">
                                      Est. {item.cookingTime} mins
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="text-right">
                                <span className="text-sm font-semibold text-foreground">
                                  x{item.quantity}
                                </span>
                                {item.status === "cooking" && item.startedAt && (
                                  <div className="text-xs text-muted-foreground">
                                    Started {formatTimeAgo(item.startedAt)}
                                  </div>
                                )}
                              </div>
                            </div>

                            {(item.notes || item.extras) && (
                              <div className="mt-2 space-y-1">
                                {item.notes && (
                                  <p className="text-xs text-foreground bg-muted rounded px-2 py-1">
                                    📝 {item.notes}
                                  </p>
                                )}
                                {item.extras && (
                                  <p className="text-xs text-secondary-foreground bg-secondary/30 rounded px-2 py-1">
                                    ➕ {item.extras}
                                  </p>
                                )}
                              </div>
                            )}

                            {/* Quick action buttons */}
                            <div className="mt-3 flex gap-2">
                              {item.status === "pending" && (
                                <motion.button
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  onClick={() => handleItemStatusChange(order.id, item.id, "cooking")}
                                  className="flex-1 bg-gradient-to-r from-secondary to-primary hover:from-secondary/90 hover:to-primary/90 text-primary-foreground text-xs py-2 px-3 rounded-md transition-all duration-200"
                                >
                                  Start Cooking
                                </motion.button>
                              )}
                              {item.status === "cooking" && (
                                <motion.button
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  onClick={() => handleItemStatusChange(order.id, item.id, "ready")}
                                  className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground text-xs py-2 px-3 rounded-md transition-all duration-200"
                                >
                                  Mark Ready
                                </motion.button>
                              )}
                              {item.status === "ready" && (
                                <div className="flex-1 bg-success/10 text-success text-xs py-2 px-3 rounded-md text-center">
                                  ✅ Ready to Serve
                                </div>
                              )}
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </ScrollArea>

                    {/* Order completion button */}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="pt-3 border-t border-slate-600/50"
                    >
                      {order.completedItems === order.totalItems ? (
                        <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-3 shadow-lg">
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Order Complete - Send to Service
                        </Button>
                      ) : (
                        <div className="text-center text-muted-foreground text-sm py-2">
                          {order.completedItems}/{order.totalItems} items ready
                        </div>
                      )}
                    </motion.div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      </div>
    </div>
  );
};

export default StationKDS;
