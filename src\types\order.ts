export interface orderTableTypes {
  id: number;
  table_number: string;
  capacity: number;
  is_active: boolean;
  created_at: string;
  modified_at: string;
  revenue_center: string;
}

export interface orderLineTypes {
  id: number;
  order: string;
  menu_item: string;
  menu_item_name?: string;
  quantity: string;
  unit_price: string;
  modifiers: string;
  special_instructions: string;
  line_total: string;
  status?: string;
}

export interface orderTypes {
  id: number;
  order_number: string;
  order_type: string;
  status: string;
  table_number: number;
  order_date: string;
  total_amount: string;
  payment_status: boolean;
  guest_count: number | null;
  created_by: string | null;
  created_at: string;
  modified_at: string;
  tax_amount: string;
  service_charge: string | null;
  catering_levy: string | null;
  orderline_items: orderLineTypes[];
}
