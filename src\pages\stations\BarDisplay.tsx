import React, { useEffect, useState, useCallback } from "react";
import { Link } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  Timer,
  CupSoda,
  Clock,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface DrinkItem {
  id: string;
  name: string;
  quantity: number;
  notes?: string;
  status: "pending" | "mixing" | "ready" | "cancelled";
  mixingTime?: number; // minutes
  startedAt?: Date;
  estimatedCompletion?: Date;
}

interface BarOrder {
  id: string;
  ticketNumber: string;
  tableNumber?: string;
  timeReceived: Date;
  priority: "normal" | "rush" | "vip";
  items: DrinkItem[];
  completedItems: number;
  totalItems: number;
}

const BarDisplay: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [dense, setDense] = useState(false);

  const [orders, setOrders] = useState<BarOrder[]>([
    {
      id: "b1",
      ticketNumber: "BAR-101",
      tableNumber: "B12",
      timeReceived: new Date(Date.now() - 5 * 60 * 1000),
      priority: "normal",
      items: [
        {
          id: "b1-1",
          name: "Mojito",
          quantity: 2,
          status: "mixing",
          mixingTime: 4,
          startedAt: new Date(Date.now() - 2 * 60 * 1000),
          estimatedCompletion: new Date(Date.now() + 2 * 60 * 1000),
        },
        { id: "b1-2", name: "Beer Pint", quantity: 1, status: "ready" },
      ],
      completedItems: 1,
      totalItems: 2,
    },
    {
      id: "b2",
      ticketNumber: "BAR-102",
      timeReceived: new Date(Date.now() - 12 * 60 * 1000),
      priority: "rush",
      items: [
        {
          id: "b2-1",
          name: "Old Fashioned",
          quantity: 1,
          status: "mixing",
          mixingTime: 5,
          startedAt: new Date(Date.now() - 3 * 60 * 1000),
          estimatedCompletion: new Date(Date.now() + 2 * 60 * 1000),
        },
        { id: "b2-2", name: "House Wine (Glass)", quantity: 2, status: "pending" },
      ],
      completedItems: 0,
      totalItems: 2,
    },
  ]);

  // rush alert
  useEffect(() => {
    const hasRush = orders.some(o => o.priority === 'rush' && o.completedItems < o.totalItems);
    if (hasRush) {
      const audio = new Audio('/sounds/rush-alert.mp3');
      audio.volume = 0.4;
      audio.play().catch(() => {});
    }
  }, [orders]);

  // persist density
  useEffect(() => {
    const stored = localStorage.getItem("bar_dense");
    if (stored) setDense(stored === "1");
  }, []);
  useEffect(() => {
    localStorage.setItem("bar_dense", dense ? "1" : "0");
  }, [dense]);

  // search & filters
  const [search, setSearch] = useState<string>(() => localStorage.getItem('bar_search') || "");
  const [priorityFilter, setPriorityFilter] = useState<"all" | "normal" | "rush" | "vip">(
    () => (localStorage.getItem('bar_priority') as any) || "all"
  );
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'mixing' | 'ready' | 'cancelled'>(
    () => (localStorage.getItem('bar_status') as any) || 'all'
  );
  useEffect(() => localStorage.setItem('bar_search', search), [search]);
  useEffect(() => localStorage.setItem('bar_priority', priorityFilter), [priorityFilter]);
  useEffect(() => localStorage.setItem('bar_status', statusFilter), [statusFilter]);


  useEffect(() => {
    const t = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(t);
  }, []);

  const formatTimeAgo = (date: Date): string => {
    const mins = Math.floor((currentTime.getTime() - date.getTime()) / (1000 * 60));
    if (mins < 1) return "Just now";
    if (mins === 1) return "1 min ago";
    return `${mins} mins ago`;
  };

  const formatTimeRemaining = (date?: Date): string => {
    if (!date) return "--";
    const mins = Math.floor((date.getTime() - currentTime.getTime()) / (1000 * 60));
    if (mins <= 0) return "Ready!";
    if (mins === 1) return "1 min";
    return `${mins} mins`;
  };

  const getOrderProgress = (order: BarOrder): number =>
    Math.round((order.completedItems / order.totalItems) * 100);

  const priorityBarClass = (priority: string) => {
    switch (priority) {
      case "vip":
        return "from-accent to-primary";
      case "rush":
        return "from-primary to-secondary";
      default:
        return "from-secondary to-primary";
    }
  };

  const handleStatusChange = useCallback((orderId: string, itemId: string, newStatus: DrinkItem["status"]) => {
    setOrders(prev => prev.map(o => {
      if (o.id !== orderId) return o;
      const items = o.items.map(it => {
        if (it.id !== itemId) return it;
        const updated: DrinkItem = { ...it, status: newStatus };
        if (newStatus === "mixing" && !it.startedAt) {
          updated.startedAt = new Date();
          updated.estimatedCompletion = new Date(Date.now() + (it.mixingTime || 3) * 60 * 1000);
        }
        return updated;
      });
      const completedItems = items.filter(i => i.status === "ready").length;
      return { ...o, items, completedItems };
    }));
  }, []);

  return (
    <div className="min-h-screen bg-background p-4 relative">
      <div className="mx-auto space-y-6">
        <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }} className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link to="/stations/home">
              <Button className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg">
                <ArrowLeft className="h-4 w-4" /> Back to Menu
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">Bar Display System</h1>
              <p className="text-muted-foreground mt-1">Fast drink service • {currentTime.toLocaleTimeString()}</p>
            </div>
          </div>
          <div className="flex items-center gap-6">
            <div className="hidden md:flex items-center gap-2 pr-2">
              <span className="text-xs text-muted-foreground">Compact</span>
              <Switch checked={dense} onCheckedChange={setDense} />
            </div>
            <div className="flex gap-4">
              <div className="bg-card/80 border border-border rounded-xl p-4 text-center shadow-lg">
                <div className="text-2xl font-bold text-primary">{orders.filter(o => o.completedItems === o.totalItems).length}</div>
                <div className="text-xs text-muted-foreground">Completed</div>
              </div>
              <div className="bg-card/80 border border-border rounded-xl p-4 text-center shadow-lg">
                <div className="text-2xl font-bold text-secondary">{orders.filter(o => o.completedItems < o.totalItems).length}</div>
                <div className="text-xs text-muted-foreground">In Progress</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Toolbar: Filters + Density */}
        <div className="flex items-center justify-between gap-3 flex-wrap">
          <div className="flex items-center gap-3">
            <div className="w-56">
              <Input value={search} onChange={(e) => setSearch(e.target.value)} placeholder="Search tickets or items..." />
            </div>
            <div className="w-40">
              <Select value={priorityFilter} onValueChange={(v) => setPriorityFilter(v as any)}>
                <SelectTrigger><SelectValue placeholder="Priority" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="rush">Rush</SelectItem>
                  <SelectItem value="vip">VIP</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-40">
              <Select value={statusFilter} onValueChange={(v) => setStatusFilter(v as any)}>
                <SelectTrigger><SelectValue placeholder="Status" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="mixing">Mixing</SelectItem>
                  <SelectItem value="ready">Ready</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Compact</span>
            <Switch checked={dense} onCheckedChange={setDense} />
          </div>
        </div>

        {/* Orders Grid */}
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.2 }} className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
          <AnimatePresence>
            {orders
              .filter(order => {
                const matchesPriority = priorityFilter === 'all' || order.priority === priorityFilter;
                if (!matchesPriority) return false;
                const hay = `${order.ticketNumber} ${order.tableNumber || ''} ${order.items.map(i => i.name).join(' ')}`.toLowerCase();
                return hay.includes(search.toLowerCase());
              })
              .filter(order => {
                if (statusFilter !== 'all' && !order.items.some(i => i.status === statusFilter)) return false;
                return true;
              })
              .map((order, index) => (
              <motion.div key={order.id} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -20 }} transition={{ delay: index * 0.05 }}>
                <Card className={`bg-card/90 border border-border shadow-xl backdrop-blur-sm flex flex-col ${dense ? 'h-[380px]' : 'h-[460px]'} md:${dense ? 'h-[420px]' : 'h-[500px]'} xl:${dense ? 'h-[440px]' : 'h-[540px]'}`}>
                  <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${priorityBarClass(order.priority)} ${order.priority === 'rush' ? 'kds-urgent-order' : ''}`} />

                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-bold text-foreground flex items-center gap-2">
                        <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">Ticket {order.ticketNumber}</span>
                      </CardTitle>
                      <Badge className="text-xs font-semibold px-3 py-1 rounded-full border-0 bg-secondary text-secondary-foreground">
                        {order.priority.toUpperCase()}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span className="flex items-center gap-1"><Clock className="h-4 w-4" /> {formatTimeAgo(order.timeReceived)}</span>
                      {order.tableNumber && <span className="text-muted-foreground">Table {order.tableNumber}</span>}
                    </div>
                    <div className="mt-3">
                      <div className="flex justify-between text-xs text-muted-foreground mb-1"><span>Progress</span><span>{getOrderProgress(order)}%</span></div>
                      <Progress value={getOrderProgress(order)} className="h-2 bg-muted" />
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4 flex-1 min-h-0 flex flex-col">
                    <ScrollArea className="flex-1 min-h-0">
                      <div className="space-y-2">
                        {order.items.map((item) => (
                          <div key={item.id} className={`border rounded-md p-3 ${item.status === 'ready' ? 'bg-success/10 border-success/40' : item.status === 'mixing' ? 'bg-primary/10 border-primary/40' : item.status === 'pending' ? 'bg-secondary/10 border-secondary/40' : 'bg-destructive/10 border-destructive/40'}`}>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                {item.status === 'pending' && <Timer className="h-5 w-5 text-secondary" />}
                                {item.status === 'mixing' && <CupSoda className="h-5 w-5 text-primary" />}
                                {item.status === 'ready' && <CheckCircle className="h-5 w-5 text-primary" />}
                                {item.status === 'cancelled' && <XCircle className="h-5 w-5 text-destructive" />}
                                <div>
                                  <div className="font-medium text-foreground">{item.name}</div>
                                  {item.mixingTime && <div className="text-xs text-muted-foreground">Est. {item.mixingTime} mins</div>}
                                </div>
                              </div>
                              <div className="text-right">
                                <span className="text-sm font-semibold text-foreground">x{item.quantity}</span>
                                {item.status === 'mixing' && <div className="text-xs text-muted-foreground">{formatTimeRemaining(item.estimatedCompletion)}</div>}
                              </div>
                            </div>
                            {/* Actions */}
                            <div className="mt-3 flex gap-2">
                              {item.status === 'pending' && (
                                <Button size="sm" className="flex-1 bg-gradient-to-r from-secondary to-primary hover:from-secondary/90 hover:to-primary/90 text-primary-foreground" onClick={() => handleStatusChange(order.id, item.id, 'mixing')}>Start Mixing</Button>
                              )}
                              {item.status === 'mixing' && (
                                <Button size="sm" className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground" onClick={() => handleStatusChange(order.id, item.id, 'ready')}>Mark Ready</Button>
                              )}
                              {item.status === 'ready' && (
                                <div className="flex-1 bg-success/10 text-success text-xs py-2 px-3 rounded-md text-center">Ready</div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>

                    <div className="pt-3 border-t border-border/50">
                      {order.completedItems === order.totalItems ? (
                        <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-semibold">Complete Ticket</Button>
                      ) : (
                        <div className="text-center text-muted-foreground text-sm py-2">{order.completedItems}/{order.totalItems} items ready</div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      </div>
    </div>
  );
};

export default BarDisplay;

