import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { ToastProvider } from "./components/custom/Toast/MyToast";
import { useTheme } from "./hooks/use-theme";
import { Screen } from "./app-components/layout/screen";
import Home from "./pages/Home";

import ApiDebugPanel from "./components/debug/ApiDebugPanel";
import AutoLogoutProvider from "./components/auth/AutoLogoutProvider";

import StationWelcome from "./pages/stations";
import StationPinLogin from "./pages/stations/StationPinLogin";
import StationLandingPage from "./pages/stations/StationLandingPage";
import StationMainMenu from "./pages/stations/StationMainMenu";
import RoomEnquiry from "./pages/stations/RoomEnquiry";
import CardCreation from "./pages/stations/CardCreation";
import ViewChecks from "./pages/stations/ViewChecks";
import Reports from "./pages/stations/Reports";
import StationKDS from "./pages/stations/StationKDS";
import BarDisplay from "./pages/stations/BarDisplay";
import StationOrders from "./pages/stations/StationOrders";

function App() {
  const { theme } = useTheme();

  return (
    <>
      <ToastProvider theme={theme} position="top-right" />

      <Router>
        <AutoLogoutProvider
          warningTime={13 * 60}
          logoutTime={15 * 60}
          enabled={true}
        >
          <Screen>
            <Routes>
              {/* Redirect root path to stations-auth */}
              <Route
                path="/"
                element={<Navigate to="/stations-auth" replace />}
              />
              <Route path="/home" element={<Home />} />

              <Route path="/stations-auth" element={<StationWelcome />} />
              <Route path="/stations/pin-login" element={<StationPinLogin />} />
              <Route
                path="/stations/card-login"
                element={<StationPinLogin />}
              />
              <Route path="/stations/home" element={<StationLandingPage />} />
              <Route path="/stations/main-menu" element={<StationMainMenu />} />
              <Route path="/stations/room-enquiry" element={<RoomEnquiry />} />
              <Route
                path="/stations/functions"
                element={<StationLandingPage />}
              />
              <Route
                path="/stations/card-creation"
                element={<CardCreation />}
              />
              <Route path="/stations/view-checks" element={<ViewChecks />} />
              <Route path="/stations/reports" element={<Reports />} />

              <Route path="/stations/kds" element={<StationKDS />} />
              <Route path="/stations/bar-display" element={<BarDisplay />} />

              <Route path="/stations/orders" element={<StationOrders />} />
            </Routes>

            {/* Debug Panel - only in development */}
            {import.meta.env.DEV && <ApiDebugPanel />}
          </Screen>
        </AutoLogoutProvider>
      </Router>
    </>
  );
}

export default App;
