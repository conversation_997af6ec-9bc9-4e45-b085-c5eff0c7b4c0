import { But<PERSON> } from "@/components/ui/button";
import { Plus, Minus, Trash2, Edit3, PlusCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useEffect, useState } from "react";
import { useLazyGetModifierMenusQuery } from "@/redux/slices/menus";

type Props = {
  item: any;
  noteText: string;
  calculateItemTotal: (item: any) => number;
  updateQuantity: (itemId: string, delta: number) => void;
  setOrderItems: React.Dispatch<React.SetStateAction<any[]>>;
  setNoteText: React.Dispatch<React.SetStateAction<string>>;
};

const StationOrderItem = ({
  item,
  noteText,
  calculateItemTotal,
  updateQuantity,
  setOrderItems,
  setNoteText,
}: Props) => {
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  const [
    getItemModifiers,
    { data: modifiers, isLoading: mdLoading, isFetching: mdFetching },
  ] = useLazyGetModifierMenusQuery();

  const handleFetchExtras = async () => {
    await getItemModifiers({ menu_item: item.id }).unwrap();
  };

  const handleAddNote = (itemId: string) => {
    setOrderItems((prev) =>
      prev.map((item) =>
        item.id === itemId ? { ...item, notes: noteText } : item
      )
    );
    setNoteText("");
    setSelectedItemId(null); // Close the modal by resetting selectedItemId
  };

  const handleAddExtras = (
    itemId: string,
    extra: { label: string; price: string; id: string },
    checked: boolean
  ) => {
    setOrderItems((prev) =>
      prev.map((item) => {
        if (item.id === itemId) {
          const currentExtras = item.extras || [];
          if (checked) {
            return {
              ...item,
              unit_price: item.price,
              extras: [...currentExtras, extra],
              price: item.price + extra?.price,
            };
          } else {
            return {
              ...item,
              unit_price: item.price,
              extras: currentExtras.filter((e: any) => e.id !== extra?.id),
              price: parseFloat(item.price) - parseFloat(extra?.price),
            };
          }
        }
        return item;
      })
    );
  };

  return (
    <div className="group relative bg-gradient-to-br from-card via-card to-card/95 border border-border/30 rounded-xl p-4 md:p-5 hover:shadow-xl hover:shadow-primary/5 transition-all duration-300 hover:border-primary/20 hover:-translate-y-1">
      {/* Item Header */}
      <div className="flex justify-between items-start gap-3 mb-4">
        <div className="flex-1">
          <h4 className="font-bold text-sm md:text-base text-foreground leading-tight mb-1">
            {item.name}
          </h4>
          <p className="text-xs md:text-sm text-muted-foreground">
            Ksh. {item.price} each
          </p>
        </div>
        <div className="text-right">
          <div className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground px-3 py-1.5 rounded-lg font-bold text-sm md:text-base shadow-sm">
            Ksh. {calculateItemTotal(item)}
          </div>
        </div>
      </div>

      {/* Quantity Controls */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2 bg-muted/30 rounded-lg p-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => updateQuantity(item.id, -1)}
            className="h-8 w-8 md:h-9 md:w-9 rounded-lg hover:bg-destructive/10 hover:text-destructive transition-all duration-200"
          >
            <Minus className="h-3 w-3 md:h-4 md:w-4" />
          </Button>
          <div className="bg-background border border-border/50 rounded-lg px-3 py-1.5 min-w-[3rem] text-center">
            <span className="font-semibold text-sm md:text-base">
              {item.quantity}
            </span>
          </div>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => updateQuantity(item.id, 1)}
            className="h-8 w-8 md:h-9 md:w-9 rounded-lg hover:bg-primary/10 hover:text-primary transition-all duration-200"
          >
            <Plus className="h-3 w-3 md:h-4 md:w-4" />
          </Button>
        </div>

        <Button
          size="sm"
          variant="ghost"
          onClick={() => updateQuantity(item.id, -item.quantity)}
          className="h-8 w-8 md:h-9 md:w-9 rounded-lg hover:bg-destructive/10 hover:text-destructive transition-all duration-200 text-muted-foreground hover:scale-110"
        >
          <Trash2 className="h-3 w-3 md:h-4 md:w-4" />
        </Button>
      </div>

      {/* Notes and Extras Display */}
      {(item.notes || (item.extras && item.extras.length > 0)) && (
        <div className="space-y-2 mb-4">
          {item.notes && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-3 rounded-lg">
              <div className="flex items-start gap-2">
                <span className="text-blue-600 dark:text-blue-400 text-sm">
                  📝
                </span>
                <div>
                  <span className="font-medium text-blue-700 dark:text-blue-300 text-xs md:text-sm">
                    Note:
                  </span>
                  <p className="text-blue-600 dark:text-blue-400 text-xs md:text-sm mt-1">
                    {item.notes}
                  </p>
                </div>
              </div>
            </div>
          )}

          {item.extras && item.extras.length > 0 && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-3 rounded-lg">
              <div className="flex items-start gap-2">
                <span className="text-green-600 dark:text-green-400 text-sm">
                  ✨
                </span>
                <div>
                  <span className="font-medium text-green-700 dark:text-green-300 text-xs md:text-sm">
                    Extras:
                  </span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {item.extras.map((extra: any, index: number) => (
                      <span
                        key={index}
                        className="bg-green-100 dark:bg-green-800/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-md text-xs"
                      >
                        {extra?.label} (+Ksh. {extra?.price})
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2 flex-wrap">
        <Dialog>
          <DialogTrigger asChild>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleFetchExtras()}
              className="flex-1 h-9 md:h-10 hover:scale-105 transition-all duration-200 border-border/50 hover:border-green-400 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20"
            >
              <PlusCircle className="h-3 w-3 md:h-4 md:w-4 mr-2" />
              <span className="text-xs md:text-sm font-medium">Add Extras</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px] max-w-[95vw] border-border/50">
            <DialogHeader>
              <DialogTitle className="text-foreground text-sm sm:text-base">
                Add {item?.name} Extras
              </DialogTitle>
            </DialogHeader>
            <div className="grid gap-3 py-4 max-h-[60vh] overflow-y-auto">
              {mdLoading || mdFetching ? (
                <div className="text-center text-muted-foreground text-sm">
                  Loading extras...
                </div>
              ) : modifiers?.data?.total_data < 1 ? (
                <div className="text-center text-muted-foreground text-sm">
                  No extras available
                </div>
              ) : (
                modifiers?.data?.results?.map((extra: any) => (
                  <div
                    key={extra?.id}
                    className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <Checkbox
                      id={extra?.id}
                      checked={item?.extras?.some(
                        (e: any) => e.id === extra?.id
                      )}
                      onCheckedChange={(checked) => {
                        handleAddExtras(
                          item.id,
                          {
                            label: extra?.name,
                            price: extra?.price,
                            id: extra?.id,
                          },
                          checked as boolean
                        );
                      }}
                      className="border-border/50"
                    />
                    <Label
                      htmlFor={extra?.id}
                      className="text-foreground cursor-pointer flex-1 text-sm"
                    >
                      {extra?.name}
                      <span className="text-primary font-semibold ml-2">
                        (+ Ksh. {extra?.price})
                      </span>
                    </Label>
                  </div>
                ))
              )}
            </div>
          </DialogContent>
        </Dialog>

        <Dialog>
          <DialogTrigger asChild>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setSelectedItemId(item.id);
                setNoteText(item.notes || "");
              }}
              className="flex-1 h-9 md:h-10 hover:scale-105 transition-all duration-200 border-border/50 hover:border-blue-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
            >
              <Edit3 className="h-3 w-3 md:h-4 md:w-4 mr-2" />
              <span className="text-xs md:text-sm font-medium">Add Note</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px] max-w-[95vw] border-border/50 rounded-xl">
            <DialogHeader className="pb-4">
              <DialogTitle className="text-foreground text-lg md:text-xl font-bold flex items-center gap-2">
                <span className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm">
                  📝
                </span>
                Add Special Instructions
              </DialogTitle>
              <p className="text-muted-foreground text-sm">
                Add any special instructions for this item
              </p>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <Textarea
                placeholder="e.g., No onions, extra spicy, well done..."
                value={noteText}
                onChange={(e) => setNoteText(e.target.value)}
                className="min-h-[100px] md:min-h-[120px] border-2 border-border/50 bg-background/80 backdrop-blur-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 text-sm md:text-base rounded-lg"
              />
              <DialogFooter className="flex flex-col sm:flex-row gap-3 pt-4">
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setNoteText("");
                      setSelectedItemId(null);
                    }}
                    className="w-full sm:w-auto h-10 md:h-11 border-2 hover:scale-105 transition-all duration-200 text-sm md:text-base"
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  onClick={() => handleAddNote(item.id)}
                  className="w-full sm:w-auto h-10 md:h-11 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-600 hover:scale-105 transition-all duration-200 text-sm md:text-base font-medium"
                >
                  Save Note
                </Button>
              </DialogFooter>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default StationOrderItem;
