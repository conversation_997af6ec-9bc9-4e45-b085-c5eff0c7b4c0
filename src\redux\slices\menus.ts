import { apiSlice } from "../apiSlice";

export const menuApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getMenus: builder.query({
      query: (params) => ({
        url: "/menu/menus",
        method: "GET",
        params: params,
      }),
      providesTags: ["Menu"],
    }),

    getMenuGroups: builder.query({
      query: (params) => ({
        url: "/menu/menu-groups",
        method: "GET",
        params: params,
      }),
      providesTags: ["MenuGroups"],
    }),

    getMenuSubGroups: builder.query({
      query: (params) => ({
        url: "/menu/menu-subgroups",
        method: "GET",
        params: params,
      }),
      providesTags: ["MenuGroups"],
    }),

    getMenuItems: builder.query({
      query: (params) => ({
        url: "/menu/menu-items",
        method: "GET",
        params: params,
      }),
      providesTags: ["Menu"],
    }),

    getModifierMenus: builder.query({
      query: (params) => ({
        url: "/menu/menu-item-modifiers",
        method: "GET",
        params: params,
      }),
      providesTags: ["Menu"],
    }),
  }),
});

export const {
  useGetMenusQuery,
  useGetMenuGroupsQuery,
  useGetMenuSubGroupsQuery,
  useGetMenuItemsQuery,
  useGetModifierMenusQuery,

  useLazyGetMenusQuery,
  useLazyGetMenuGroupsQuery,
  useLazyGetMenuSubGroupsQuery,
  useLazyGetMenuItemsQuery,
  useLazyGetModifierMenusQuery,
} = menuApiSlice;
