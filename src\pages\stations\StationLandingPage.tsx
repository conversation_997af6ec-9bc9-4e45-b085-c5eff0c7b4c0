import React from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Menu,
  Search,
  CalendarRange,
  CreditCard,
  FileText,
  LogOut,
  BarChart3,
  CookingPotIcon,
  NotebookPenIcon,
  Clock2Icon,
  NotebookIcon,
  CheckCheckIcon,
  GlassWater,
} from "lucide-react";
import Logo from "@/assets/logo.png";
import { usePostLogoutMutation } from "@/redux/slices/auth";
import { useAuthHook } from "@/utils/useAuthHook";
import { toast } from "sonner";

const StationLandingPage: React.FC = () => {
  const menuItems = [
    {
      icon: NotebookPenIcon,
      label: "Punch Order",
      path: "/stations/main-menu",
      isOrder: false,
      isRoom: false,
      color: "from-blue-500 to-blue-600",
    },
    {
      icon: Menu,
      label: "Orders",
      path: "/stations/orders",
      isOrder: true,
      isRoom: false,
      color: "from-blue-500 to-blue-600",
    },
    {
      icon: CookingPotIcon,
      label: "Kitchen Display",
      path: "/stations/kds",
      isOrder: false,
      isRoom: false,
      color: "from-primary to-primary",
    },
    {
      icon: GlassWater,
      label: "Bar Display",
      path: "/stations/bar-display",
      color: "from-secondary to-secondary",
      isOrder: false,
      isRoom: false,
    },
    {
      icon: FileText,
      label: "View Checks",
      path: "/stations/view-checks",
      isOrder: false,
      isRoom: false,
      color: "from-teal-500 to-teal-600",
    },
    {
      icon: Search,
      label: "Room Enquiry",
      path: "/stations/room-enquiry",
      isOrder: false,
      isRoom: true,
      color: "from-green-500 to-green-600",
    },
    // {
    //   icon: CalendarRange,
    //   label: "Functions",
    //   path: "/stations/functions",
    // isOrder: true,
    // isRoom: false,
    //   color: "from-purple-500 to-purple-600",
    // },
    // {
    //   icon: CreditCard,
    //   label: "Card Creation",
    //   path: "/stations/card-creation",
    // isOrder: true,
    // isRoom: false,
    //   color: "from-orange-500 to-orange-600",
    // },
    // {
    //   icon: BarChart3,
    //   label: "Reports",
    //   path: "/stations/reports",
    // isOrder: true,
    // isRoom: false,
    //   color: "from-indigo-500 to-indigo-600",
    // },
  ];

  const navigate = useNavigate();
  const [postLogout, { isLoading: isLoggingOut }] = usePostLogoutMutation();
  const { isAuthenticated, user_details, token } = useAuthHook();

  const handleLogout = async () => {
    try {
      if (token?.refresh_token) {
        await postLogout({ refresh_token: token.refresh_token }).unwrap();
        toast.success("Logged out successfully");
      } else {
        toast.info("Session ended");
      }
      navigate("/stations-auth");
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Logout failed, but session will be cleared");
      navigate("/stations-auth");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-5xl shadow-2xl border-border/50 backdrop-blur-sm">
        <CardHeader className="text-center space-y-6 pb-8">
          <div className="flex justify-center">
            <div className="relative">
              <img
                src={Logo}
                alt="Logo"
                className="h-20 w-auto drop-shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/10 to-transparent rounded-full blur-xl" />
            </div>
          </div>
          <div className="space-y-3">
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              POS Station Menu
            </CardTitle>
            <CardDescription className="text-base text-muted-foreground">
              Select an option to proceed with your tasks
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="px-8 pb-8 space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {menuItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <Button
                  key={index}
                  variant="outline"
                  size="lg"
                  className="h-40 flex flex-col items-center justify-center gap-4 hover:scale-105 transition-all duration-300 hover:shadow-xl border-border/50 hover:border-primary/50 group relative overflow-hidden"
                  asChild
                >
                  <Link to={item?.isRoom ? "#" : item.path}>
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${item.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}
                    />
                    <IconComponent className="h-8 w-8 group-hover:text-primary transition-colors duration-300" />
                    <span className="font-semibold text-base group-hover:text-primary transition-colors duration-300">
                      {item.label}
                    </span>
                    {item?.isRoom && <p>Coming Soon</p>}
                    {item.isOrder && (
                      <div className="flex items-center text-muted-foreground p-2">
                        <div className="flex items-center gap-3 text-sm bg-background/80 dark:bg-background/40 px-4 py-1 rounded-lg backdrop-blur-sm border border-border hover:border-primary/30 dark:hover:border-primary/50 transition-all duration-300 group-hover:shadow-md">
                          <NotebookIcon className="h-5 w-5 text-primary animate-pulse" />
                          <span className="flex items-center gap-2 bg-background/80 dark:bg-background/40 px-3 py-1 rounded-full border border-border hover:bg-background/95 dark:hover:bg-background/60 transition-all duration-300">
                            0
                            <CheckCheckIcon
                              size={13}
                              className="text-green-600 group-hover:scale-110 transition-transform duration-300"
                            />
                          </span>
                          <span className="flex items-center gap-2 bg-background/80 dark:bg-background/40 px-3 py-1 rounded-full border border-border hover:bg-background/95 dark:hover:bg-background/60 transition-all duration-300">
                            0
                            <Clock2Icon
                              size={13}
                              className="text-orange-500 group-hover:scale-110 transition-transform duration-300"
                            />
                          </span>
                        </div>
                      </div>
                    )}
                  </Link>
                </Button>
              );
            })}
          </div>

          <div className="flex justify-center pt-4">
            <Button
              onClick={handleLogout}
              variant="destructive"
              size="lg"
              className="h-16 px-12 flex items-center gap-3 text-lg font-semibold hover:scale-105 transition-all duration-200 shadow-lg"
              disabled={isLoggingOut}
            >
              <LogOut className="h-6 w-6" />
              Log Out
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StationLandingPage;
