import * as React from "react";
import { AppSidebar } from "@/app-components/sidebar/app-sidebar";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { useTheme } from "@/hooks/use-theme";
import { useNavigate } from "react-router-dom";
import { usePostLogoutMutation } from "@/redux/slices/auth";
import { toast } from "@/components/custom/Toast/MyToast";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Moon,
  Sun,
  LogOut,
  Calendar,
  Bell,
  Settings,
  UserRound,
  X,
  CheckCircle,
  AlertCircle,
  <PERSON><PERSON>,
  Clock,
} from "lucide-react";
import { useAuthHook } from "@/utils/useAuthHook";
import { DestructiveButton } from "@/components/custom/buttons/buttons";
import useCurrentCurrencyStore from "@/zustand/useCurrentCurrencyStore";
import sound from "@/assets/notify_sound.mp3";

interface Notification {
  id: string;
  type: "order" | "system" | "alert" | "info";
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  priority: "low" | "medium" | "high";
}

interface ScreenProps {
  children: React.ReactNode;
  headerContent?: React.ReactNode;
  showSidebar?: boolean;
}

export function Screen({
  children,
  headerContent,
  showSidebar = false,
}: ScreenProps) {
  const { toggleTheme, theme } = useTheme();
  const navigate = useNavigate();
  const [postLogout, { isLoading: isLoggingOut }] = usePostLogoutMutation();

  // Notification state
  const [notifications, setNotifications] = React.useState<Notification[]>([
    {
      id: "1",
      type: "order",
      title: "Order Ready",
      message: "Order ORD-123 is ready for pickup at Main Kitchen",
      timestamp: new Date(),
      isRead: false,
      priority: "high",
    },
    {
      id: "2",
      type: "system",
      title: "System Update",
      message: "System maintenance scheduled for tonight at 2:00 AM",
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      isRead: false,
      priority: "low",
    },
    {
      id: "3",
      type: "alert",
      title: "Low Stock Alert",
      message: "Chicken Wings are running low in inventory",
      timestamp: new Date(Date.now() - 60 * 60 * 1000),
      isRead: false,
      priority: "medium",
    },
  ]);

  // auth
  const { isAuthenticated, user_details, token } = useAuthHook();

  const handleLogout = async () => {
    try {
      if (token?.refresh_token) {
        await postLogout({ refresh_token: token.refresh_token }).unwrap();
        toast.success("Logged out successfully");
      } else {
        toast.info("Session ended");
      }
      navigate("/stations-auth");
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Logout failed, but session will be cleared");
      navigate("/stations-auth");
    }
  };

  // notification handlers
  const unreadCount = notifications.filter((n) => !n.isRead).length;

  const markAsRead = (notificationId: string) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === notificationId ? { ...n, isRead: true } : n))
    );
  };

  const closeNotificationBanner = (notificationId: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== notificationId));
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
  };

  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "order":
        return <CheckCircle className="w-4 h-4" />;
      case "alert":
        return <AlertCircle className="w-4 h-4" />;
      case "system":
        return <Settings className="w-4 h-4" />;
      default:
        return <Info className="w-4 h-4" />;
    }
  };

  const getNotificationColor = (
    type: Notification["type"],
    priority: Notification["priority"]
  ) => {
    if (priority === "high") return "bg-red-500";
    if (type === "order") return "bg-green-500";
    if (type === "alert") return "bg-orange-500";
    if (type === "system") return "bg-blue-500";
    return "bg-gray-500";
  };

  const getBannerColor = (
    type: Notification["type"],
    priority: Notification["priority"]
  ) => {
    if (priority === "high") return "bg-red-500 border-red-600";
    if (type === "order") return "bg-green-500 border-green-600";
    if (type === "alert") return "bg-orange-500 border-orange-600";
    if (type === "system") return "bg-blue-500 border-blue-600";
    return "bg-gray-500 border-gray-600";
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);

    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return timestamp.toLocaleDateString();
  };

  const soundNotification = () => {
    const audio = new Audio(sound);
    audio.play();
  };

  React.useEffect(() => {
    const timer = setInterval(() => {
      soundNotification();
    }, 3600000);
    return () => clearInterval(timer);
  }, []);

  return (
    <SidebarProvider defaultOpen={false}>
      <div className="flex h-screen w-screen overflow-hidden">
        {showSidebar && <AppSidebar />}

        <SidebarInset className="flex flex-col flex-1 w-full overflow-hidden">
          <header className="flex h-16 shrink-0 items-center justify-between px-4 bg-white text-black shadow-md dark:bg-gray-900 dark:text-gray-100 transition-all group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 border-b sticky top-0 z-10">
            <div className="flex items-center space-x-2">
              {showSidebar && (
                <>
                  <SidebarTrigger className="dark:bg-gray-700 dark:text-white" />
                  <Separator orientation="vertical" className="h-6" />
                </>
              )}
              <div>{headerContent}</div>
            </div>

            <div className="flex items-center space-x-6">
              {/* NOTIFICATION BELL */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    className="p-2 rounded-full relative hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    aria-label="Notifications"
                  >
                    <Bell className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                    {unreadCount > 0 && (
                      <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs bg-red-500 text-white">
                        {unreadCount > 9 ? "9+" : unreadCount}
                      </Badge>
                    )}
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-80 rounded-lg bg-white text-black border dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                  align="end"
                  sideOffset={6}
                >
                  <div className="flex items-center justify-between p-3 border-b">
                    <DropdownMenuLabel className="py-0 text-base font-semibold">
                      Notifications
                    </DropdownMenuLabel>
                    {unreadCount > 0 && (
                      <button
                        onClick={markAllAsRead}
                        className="text-xs text-primary hover:underline"
                      >
                        Mark all read
                      </button>
                    )}
                  </div>
                  <div className="max-h-96 overflow-y-auto">
                    {notifications.length === 0 ? (
                      <div className="p-4 text-center text-muted-foreground">
                        <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p>No notifications</p>
                      </div>
                    ) : (
                      notifications.map((notification) => (
                        <DropdownMenuItem
                          key={notification.id}
                          className="p-0 focus:bg-transparent"
                        >
                          <div
                            className={`w-full p-3 border-b last:border-b-0 hover:bg-muted/50 transition-colors ${
                              !notification.isRead ? "bg-primary/5" : ""
                            }`}
                          >
                            <div className="flex items-start gap-3">
                              <div
                                className={`p-1.5 rounded-full text-white ${getNotificationColor(
                                  notification.type,
                                  notification.priority
                                )}`}
                              >
                                {getNotificationIcon(notification.type)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-1">
                                  <p className="font-medium text-sm truncate">
                                    {notification.title}
                                  </p>
                                  {!notification.isRead && (
                                    <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2" />
                                  )}
                                </div>
                                <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                                  {notification.message}
                                </p>
                                <div className="flex items-center justify-between">
                                  <span className="text-xs text-muted-foreground flex items-center gap-1">
                                    <Clock className="w-3 h-3" />
                                    {formatTimeAgo(notification.timestamp)}
                                  </span>
                                  {!notification.isRead && (
                                    <button
                                      onClick={() =>
                                        markAsRead(notification.id)
                                      }
                                      className="text-xs text-primary hover:underline"
                                    >
                                      Mark read
                                    </button>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </DropdownMenuItem>
                      ))
                    )}
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* SETTINGS DROPDOWN */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    aria-label="Settings"
                  >
                    <Settings className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-48 rounded-lg bg-white text-black border dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                  align="end"
                  sideOffset={6}
                >
                  <DropdownMenuLabel className="py-2">
                    Settings
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={toggleTheme}
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    {theme === "dark" ? (
                      <>
                        <Sun className="w-4 h-4" />
                        Switch to Light Mode
                      </>
                    ) : (
                      <>
                        <Moon className="w-4 h-4" />
                        Switch to Dark Mode
                      </>
                    )}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* USER AVATAR + DROPDOWN */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="flex items-center gap-2 rounded-full p-0 transition-colors">
                    <Avatar className="h-10 w-10 rounded-full flex justify-center items-center border border-foreground">
                      <UserRound className="" size={23} />
                    </Avatar>
                    <div className="hidden sm:grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold text-xs">
                        {user_details?.fullnames}
                      </span>
                      <span className="truncate text-[10px]">
                        {user_details?.email}
                      </span>
                    </div>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-56 rounded-lg bg-white text-black border dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                  align="end"
                  sideOffset={6}
                >
                  <DropdownMenuLabel className="py-3">
                    My Account
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="truncate max-w-[200px] hover:!bg-transparent hover:!text-foreground">
                    {user_details?.fullnames}
                  </DropdownMenuItem>
                  <DropdownMenuItem className="truncate max-w-[200px] hover:!bg-transparent hover:!text-foreground">
                    {user_details?.email}
                  </DropdownMenuItem>
                  <DropdownMenuItem className="truncate max-w-[200px] hover:!bg-transparent hover:!text-foreground">
                    {user_details?.department} Dept.
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="dark:bg-gray-700" />
                  {isAuthenticated ? (
                    <DropdownMenuItem>
                      <DestructiveButton
                        size="sm"
                        className="w-full flex item-center gap-2"
                        onClick={handleLogout}
                        disabled={isLoggingOut}
                      >
                        <LogOut size={20} />
                        {isLoggingOut ? "Logging out..." : "Log out"}
                      </DestructiveButton>
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem className="hover:!bg-none">
                      <DestructiveButton
                        size="sm"
                        className="w-full flex item-center gap-2"
                        onClick={() => navigate("/stations-auth")}
                      >
                        <LogOut className="w-4 h-4" />
                        Log In
                      </DestructiveButton>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>

          <main className="flex-1 overflow-auto bg-white text-black dark:bg-gray-900 dark:text-gray-100">
            <div className="h-full w-full p-4 flex flex-col">
              {/* Notification Banners */}
              <div className="z-30 fixed top-16 left-1/2 transform -translate-x-1/2 w-full max-w-3xl px-4">
                {notifications
                  .filter((n) => !n.isRead && n.priority === "high")
                  .map((notification) => (
                    <div
                      key={notification.id}
                      className={`${getBannerColor(
                        notification.type,
                        notification.priority
                      )} text-white px-4 py-3 rounded-lg border-l-4 shadow-sm animate-in slide-in-from-top-2 duration-300 mb-2`}
                    >
                      <div className="flex justify-between items-start gap-3">
                        <div className="flex items-start gap-3 flex-1">
                          <div className="mt-0.5">
                            {getNotificationIcon(notification.type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-sm mb-1">
                              {notification.title}
                            </h3>
                            <p className="text-sm opacity-90">
                              {notification.message}
                            </p>
                            <div className="flex items-center gap-4 mt-2">
                              <span className="text-xs opacity-75 flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {formatTimeAgo(notification.timestamp)}
                              </span>
                              <button
                                onClick={() => markAsRead(notification.id)}
                                className="text-xs underline hover:no-underline opacity-90 hover:opacity-100"
                              >
                                Mark as read
                              </button>
                            </div>
                          </div>
                        </div>
                        <button
                          onClick={() =>
                            closeNotificationBanner(notification.id)
                          }
                          className="p-1 hover:bg-white/20 rounded-full transition-colors flex-shrink-0"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
              </div>
              {children}
            </div>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
