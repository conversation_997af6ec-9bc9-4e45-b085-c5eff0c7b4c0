import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  FileText,
  Clock,
  User,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Loader2Icon,
} from "lucide-react";
import { useGetOrdersQuery } from "@/redux/slices/order";
import { orderLineTypes, orderTypes } from "@/types/order";
import { formatDateTime } from "@/utils/formatDate";
import { addComma } from "@/utils/helpers";

interface OrderItem {
  id: string;
  tableNumber: string;
  orderNumber: string;
  items: {
    name: string;
    quantity: number;
    price: number;
    notes?: string;
    status: "pending" | "processing" | "completed" | "cancelled";
  }[];
  totalAmount: number;
  status: "active" | "completed" | "cancelled";
  timeCreated: string;
  server: string;
}

const StationOrders: React.FC = () => {
  const [activeTab, setActiveTab] = useState<
    "Open" | "In Progress" | "Completed" | "Ready" | "Served" | "Cancelled"
  >("Open");
  const {
    data: orders,
    isLoading,
    isFetching,
    refetch,
  } = useGetOrdersQuery({ status: activeTab });

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/stations/home">
            <Button className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:scale-105 transition-all duration-200">
              <ArrowLeft className="h-4 w-4" />
              Back to Menu
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              My Orders
            </h1>
            <p className="text-muted-foreground">
              View and manage your current shift orders
            </p>
          </div>
          <Button onClick={() => refetch()} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
        <div className="flex flex-wrap gap-2 mb-4">
          <div className="w-full sm:w-auto">
            <Button
              className="w-full sm:w-auto"
              variant={activeTab === "Open" ? "default" : "outline"}
              onClick={() => setActiveTab("Open")}
            >
              Active
            </Button>
          </div>
          <div className="w-full sm:w-auto">
            <Button
              className="w-full sm:w-auto"
              variant={activeTab === "In Progress" ? "default" : "outline"}
              onClick={() => setActiveTab("In Progress")}
            >
              In Progress
            </Button>
          </div>
          <div className="w-full sm:w-auto">
            <Button
              className="w-full sm:w-auto"
              variant={activeTab === "Ready" ? "default" : "outline"}
              onClick={() => setActiveTab("Ready")}
            >
              Ready
            </Button>
          </div>
          <div className="w-full sm:w-auto">
            <Button
              className="w-full sm:w-auto"
              variant={activeTab === "Served" ? "default" : "outline"}
              onClick={() => setActiveTab("Served")}
            >
              Served
            </Button>
          </div>
          <div className="w-full sm:w-auto">
            <Button
              className="w-full sm:w-auto"
              variant={activeTab === "Completed" ? "default" : "outline"}
              onClick={() => setActiveTab("Completed")}
            >
              Completed
            </Button>
          </div>
          <div className="w-full sm:w-auto">
            <Button
              className="w-full sm:w-auto"
              variant={activeTab === "Cancelled" ? "default" : "outline"}
              onClick={() => setActiveTab("Cancelled")}
            >
              Cancelled
            </Button>
          </div>
        </div>

        {isLoading || isFetching ? (
          <div className="text-center p-4 w-full flex justify-center items-center">
            <Loader2Icon className="animate-spin" size={30} />
          </div>
        ) : (
          <ScrollArea className="h-[calc(100vh-250px)]">
            <div className="grid md:grid-cols-2 grid-cols-1 gap-4">
              {orders?.map((order: orderTypes) => (
                <Card key={order?.id} className="shadow-md">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">
                          {order?.order_type == "Dine In"
                            ? `Dine In - Table ${order?.table_number}`
                            : order?.order_type}
                        </CardTitle>
                        <div className="text-sm text-muted-foreground">
                          Order #{order?.order_number}
                        </div>
                      </div>
                      <Badge
                        variant={
                          order?.status === "active"
                            ? "secondary"
                            : order?.status === "completed"
                            ? "outline"
                            : "destructive"
                        }
                      >
                        {order?.status.toUpperCase()}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        {order?.orderline_items?.map(
                          (item: orderLineTypes, index: number) => (
                            <div
                              key={index}
                              className="flex justify-between items-center"
                            >
                              <div className="flex items-center gap-2">
                                {item.status === "completed" ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : item.status === "processing" ? (
                                  <Clock className="h-4 w-4 text-yellow-500" />
                                ) : item.status === "cancelled" ? (
                                  <XCircle className="h-4 w-4 text-red-500" />
                                ) : (
                                  <AlertCircle className="h-4 w-4 text-blue-500" />
                                )}
                                <span>
                                  {item.quantity}x {item.menu_item_name}
                                </span>
                              </div>
                              <span>
                                Kshs.
                                {addComma(item?.line_total)}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                      <div className="flex justify-between items-center pt-2 border-t">
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {formatDateTime(order?.created_at)}
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            {order?.created_by || "Unknown"}
                          </div>
                        </div>
                        <div className="font-semibold">
                          Total: Kshs. {addComma(order?.total_amount)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        )}
      </div>
    </div>
  );
};

export default StationOrders;
