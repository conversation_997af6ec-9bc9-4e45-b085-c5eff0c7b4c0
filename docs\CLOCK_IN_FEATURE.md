# Clock-In Feature Documentation

## Overview

The Clock-In feature provides a clean, aesthetically pleasing time tracking system that allows users to clock in and out of their shifts. The feature includes a real-time counter that persists until the user clocks out.

## Features

- ✅ Clean, modern UI design with gradient backgrounds
- ✅ Real-time clock display showing current time and date
- ✅ Shift timer that counts elapsed time in HH:MM:SS format
- ✅ Persistent state across page refreshes using localStorage
- ✅ Visual indicators for active/inactive shift status
- ✅ Responsive design that works on all screen sizes
- ✅ Error handling and user feedback via toast notifications
- ✅ Integration with Redux for API state management

## Components

### 1. ClockInWidget (`src/components/clock-in/ClockInWidget.tsx`)

The main UI component that displays:

- Current time and date
- Shift status (active/inactive)
- Elapsed time counter for active shifts
- Clock In/Clock Out buttons
- User information

### 2. useShiftManager Hook (`src/hooks/useShiftManager.ts`)

Custom hook that manages:

- Shift state persistence in localStorage
- Real-time elapsed time calculations
- API synchronization
- Cross-tab synchronization via storage events

### 3. Shifts API Slice (`src/redux/slices/shifts.ts`)

Redux API slice with endpoints for:

- `getShifts` - Get all shifts with filtering
- `createShift` - Create a new shift
- `getShift` - Get specific shift by code
- `updateShift` - Update shift by code
- `deleteShift` - Delete shift by code
- `getShiftEntries` - Get all shift entries
- `clockIn` - Create a new shift entry (clock in)
- `getShiftEntry` - Get specific shift entry by code
- `clockOut` - Update shift entry with end time (clock out)
- `deleteShiftEntry` - Delete shift entry by code

## API Endpoints

The feature uses the following backend endpoints:

```
GET /shifts/
POST /shifts/
GET /shifts/{code}
PATCH /shifts/{code}
DELETE /shifts/{code}
GET /shifts/shift-entries
POST /shifts/shift-entries
GET /shifts/shift-entries/{code}
PATCH /shifts/shift-entries/{code}
DELETE /shifts/shift-entries/{code}
```

## Integration

The ClockInWidget is integrated into the StationMainMenu page in the left sidebar, positioned right after the "Back to Main Menu" button.

## Usage

### For Users

1. Navigate to the main menu after logging in
2. The clock-in widget appears in the left sidebar
3. Click "Clock In" to start your shift
4. The timer will start counting and persist across page refreshes
5. Click "Clock Out" to end your shift

### For Developers

```tsx
import ClockInWidget from "@/components/clock-in/ClockInWidget";

// Use in any component
<ClockInWidget className="mb-4" />;
```

## State Management

The feature uses a hybrid approach:

- **localStorage**: For persistence across sessions and page refreshes
- **Redux**: For API calls and server synchronization
- **React State**: For real-time UI updates (timer, current time)

## Data Flow

1. Component mounts → Load from localStorage → Sync with API
2. User clicks Clock In → Create/Get Shift → Create Shift Entry → Update localStorage → Update UI
3. Timer updates every second → Calculate elapsed time from shift entry → Update display
4. User clicks Clock Out → Update Shift Entry with end time → Clear localStorage → Update UI

## How It Works

The system uses a two-tier approach:

- **Shifts**: Represent a work day/period for an employee
- **Shift Entries**: Represent individual clock in/out events within a shift

This allows for multiple clock in/out events during a single work day (e.g., breaks, lunch).

## Error Handling

- Network errors are handled gracefully with toast notifications
- localStorage errors are caught and handled silently
- Missing user data is validated before API calls
- Fallback to local state if API is unavailable

## Testing

A test page is available at `/test/clock-in` for development and testing purposes.

## Styling

The component uses:

- Tailwind CSS for styling
- Gradient backgrounds for visual appeal
- Consistent spacing and typography
- Responsive design patterns
- Dark mode support

## Future Enhancements

- Shift break tracking
- Overtime calculations
- Shift reports and analytics
- Manager approval workflows
- Geolocation verification
- Photo capture for clock in/out
