import React from 'react';
import { useAutoLogout } from '@/hooks/useAutoLogout';
import AutoLogoutWarningModal from './AutoLogoutWarningModal';
import { useAuthHook } from '@/utils/useAuthHook';

interface AutoLogoutProviderProps {
  children: React.ReactNode;
  warningTime?: number;
  logoutTime?: number;
  enabled?: boolean;
}

const AutoLogoutProvider: React.FC<AutoLogoutProviderProps> = ({
  children,
  warningTime = 13 * 60,
  logoutTime = 15 * 60,
  enabled = true
}) => {
  const { isAuthenticated } = useAuthHook();
  
  const { state, extendSession, forceLogout } = useAutoLogout({
    warningTime,
    logoutTime,
    enabled: enabled && isAuthenticated
  });

  return (
    <>
      {children}

      <AutoLogoutWarningModal
        isOpen={state.isWarningVisible}
        timeRemaining={state.timeRemaining}
        onExtendSession={extendSession}
        onLogoutNow={forceLogout}
      />
    </>
  );
};

export default AutoLogoutProvider;
